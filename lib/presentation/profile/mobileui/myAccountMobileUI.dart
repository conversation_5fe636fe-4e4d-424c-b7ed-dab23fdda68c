import 'package:flutter/material.dart';

void main() => runApp(MaterialApp(home: MyAccountPage()));

class MyAccountPage extends StatelessWidget {
  const MyAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.red),
          onPressed: () {},
        ),
        title: Text('My Account', style: TextStyle(color: Colors.red)),
        centerTitle: false,
        actions: [
          Icon(Icons.settings, color: Colors.grey),
          SizedBox(width: 12),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Profile Card
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(color: Colors.grey.shade200, blurRadius: 6)
                ],
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 36,
                    backgroundImage: AssetImage(
                        'assets/profile.jpg'), // replace with actual asset
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Mark Anderson',
                            style: TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 18)),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.email, color: Colors.red, size: 16),
                            SizedBox(width: 6),
                            Text('<EMAIL>',
                                style: TextStyle(fontSize: 13)),
                          ],
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.phone, color: Colors.red, size: 16),
                            SizedBox(width: 6),
                            Text('0123456789', style: TextStyle(fontSize: 13)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 24),
            // Current Plan
            Align(
              alignment: Alignment.centerLeft,
              child: Text('Your Current plan',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            ),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(color: Colors.grey.shade200, blurRadius: 6)
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Basic Plan', style: TextStyle(fontSize: 17)),
                        SizedBox(height: 6),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text('\$70',
                          style: TextStyle(
                              fontSize: 24,
                              color: Colors.red,
                              fontWeight: FontWeight.bold)),
                      Text('/Yearly',
                          style:
                              TextStyle(fontSize: 12, color: Colors.grey[600])),
                    ],
                  )
                ],
              ),
            ),
            SizedBox(height: 12),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.0, vertical: 14.0),
                child: Text('Upgrade Plan', style: TextStyle(fontSize: 16)),
              ),
              onPressed: () {},
            ),
            SizedBox(height: 24),
            // Payment Info
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(color: Colors.grey.shade200, blurRadius: 6)
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Billed Yearly for \$70',
                      style: TextStyle(fontSize: 15, color: Colors.grey[700])),
                  SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Next payment Renew:',
                          style: TextStyle(fontSize: 14)),
                      Text('12/10/2025',
                          style: TextStyle(fontSize: 15, color: Colors.blue)),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
      backgroundColor: Colors.grey[100],
    );
  }
}
